# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

import re
import logging
import asyncio
from functools import partial

import odoo
from odoo import http
from odoo.http import request
from odoo.http.async_controllers import AsyncController, async_route
from odoo.service.async_db import async_exp_create_database, async_exp_drop, async_exp_list
from odoo.tools.translate import _

_logger = logging.getLogger(__name__)

DBNAME_PATTERN = '^[a-zA-Z0-9][a-zA-Z0-9_.-]+$'


class AsyncDatabaseController(AsyncController):
    """Async version of database management controller."""

    @async_route('/web/database/async_create', type='http', auth="none", methods=['POST'], csrf=False)
    async def async_create(self, master_pwd, name, lang, password, **post):
        """Create database asynchronously with proper timing."""
        insecure = odoo.tools.config.verify_admin_password('admin')
        if insecure and master_pwd:
            # Change admin password if needed
            from odoo.service.async_db import async_dispatch
            await async_dispatch('change_admin_password', ["admin", master_pwd])
        
        try:
            if not re.match(DBNAME_PATTERN, name):
                raise Exception(_('Houston, we have a database naming issue! Make sure you only use letters, numbers, underscores, hyphens, or dots in the database name, and you\'ll be golden.'))
            
            # country code could be = "False" which is actually True in python
            country_code = post.get('country_code') or False
            
            # Create database asynchronously
            await async_exp_create_database(
                name, 
                bool(post.get('demo')), 
                lang, 
                password, 
                post['login'], 
                country_code, 
                post['phone']
            )
            
            # Wait a bit for registry to be fully loaded
            await asyncio.sleep(0.5)
            
            # Authenticate user after database is ready
            credential = {'login': post['login'], 'password': password, 'type': 'password'}
            
            # Use async authentication with retry mechanism
            max_retries = 5
            for attempt in range(max_retries):
                try:
                    await self._async_authenticate_with_retry(name, credential)
                    request.session.db = name
                    break
                except Exception as e:
                    if attempt < max_retries - 1:
                        _logger.warning(f"Authentication attempt {attempt + 1} failed, retrying: {e}")
                        await asyncio.sleep(1.0)  # Wait longer between retries
                    else:
                        raise
            
            return request.redirect('/odoo')
            
        except Exception as e:
            _logger.exception("Async database creation error.")
            error = "Database creation error: %s" % (str(e) or repr(e))
            return self._render_template(error=error)

    async def _async_authenticate_with_retry(self, dbname, credential):
        """Authenticate with retry mechanism to handle timing issues."""
        from odoo.http.async_session import async_authenticate_session, wait_for_authentication_ready

        # Wait for database to be ready for authentication
        await wait_for_authentication_ready(dbname, timeout=30)

        # Perform async authentication
        await async_authenticate_session(request.session, dbname, credential)

    @async_route('/web/database/async_drop', type='http', auth="none", methods=['POST'], csrf=False)
    async def async_drop(self, master_pwd, name):
        """Drop database asynchronously."""
        insecure = odoo.tools.config.verify_admin_password('admin')
        if insecure and master_pwd:
            from odoo.service.async_db import async_dispatch
            await async_dispatch('change_admin_password', ["admin", master_pwd])
        
        try:
            await async_exp_drop(name)
            if request.session.db == name:
                request.session.logout()
            return request.redirect('/web/database/manager')
        except Exception as e:
            _logger.exception("Async database deletion error.")
            error = "Database deletion error: %s" % (str(e) or repr(e))
            return self._render_template(error=error)

    @async_route('/web/database/async_list', type='json', auth='none')
    async def async_list(self):
        """List databases asynchronously."""
        try:
            return await async_exp_list()
        except Exception as e:
            _logger.exception("Error listing databases asynchronously")
            return []

    def _render_template(self, error=None):
        """Render database management template with error."""
        # Import here to avoid circular imports
        from addons.web.controllers.database import Database
        db_controller = Database()
        return db_controller._render_template(error=error)


# Monkey patch the original database controller to add async routes
def patch_database_controller():
    """Add async routes to the existing database controller."""
    from addons.web.controllers.database import Database
    
    # Add async create method
    async def async_create_patched(self, master_pwd, name, lang, password, **post):
        async_controller = AsyncDatabaseController()
        return await async_controller.async_create(master_pwd, name, lang, password, **post)
    
    # Patch the create method to be async-aware
    original_create = Database.create
    
    def create_with_async_fallback(self, master_pwd, name, lang, password, **post):
        """Enhanced create method that can handle async operations."""
        try:
            # Try async creation first if we're in an async context
            if hasattr(request, '_async_request') and request._async_request:
                # We're in an async context, use async creation
                loop = asyncio.get_event_loop()
                async_controller = AsyncDatabaseController()
                return loop.run_until_complete(
                    async_controller.async_create(master_pwd, name, lang, password, **post)
                )
            else:
                # Fall back to original sync method
                return original_create(self, master_pwd, name, lang, password, **post)
        except Exception as e:
            _logger.exception("Database creation failed, falling back to sync method")
            return original_create(self, master_pwd, name, lang, password, **post)
    
    Database.create = create_with_async_fallback
    Database.async_create = async_create_patched


# Apply the patch when module is loaded
patch_database_controller()
