# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Async file utilities for Odoo.
This module provides async versions of file operations to improve ASGI performance.
"""

import asyncio
import aiofiles
import os
import logging
from functools import partial

_logger = logging.getLogger(__name__)


async def async_file_read(file_path, mode='rb'):
    """Read file asynchronously."""
    try:
        async with aiofiles.open(file_path, mode) as f:
            return await f.read()
    except (IOError, OSError) as e:
        _logger.info("async_file_read reading %s failed: %s", file_path, e, exc_info=True)
        return b'' if 'b' in mode else ''


async def async_file_write(file_path, data, mode='wb'):
    """Write file asynchronously."""
    try:
        # Ensure directory exists
        dirname = os.path.dirname(file_path)
        if dirname and not os.path.exists(dirname):
            await async_makedirs(dirname)
        
        async with aiofiles.open(file_path, mode) as f:
            await f.write(data)
        return True
    except (<PERSON>OError, OSError) as e:
        _logger.info("async_file_write writing %s failed: %s", file_path, e, exc_info=True)
        return False


async def async_file_exists(file_path):
    """Check if file exists asynchronously."""
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(None, os.path.exists, file_path)


async def async_makedirs(path, exist_ok=True):
    """Create directories asynchronously."""
    loop = asyncio.get_event_loop()
    makedirs_func = partial(os.makedirs, path, exist_ok=exist_ok)
    await loop.run_in_executor(None, makedirs_func)


async def async_file_delete(file_path):
    """Delete file asynchronously."""
    try:
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, os.unlink, file_path)
        return True
    except (IOError, OSError) as e:
        _logger.info("async_file_delete deleting %s failed: %s", file_path, e, exc_info=True)
        return False


async def async_walk(top):
    """Async version of os.walk."""
    loop = asyncio.get_event_loop()
    walk_func = partial(os.walk, top)
    result = await loop.run_in_executor(None, walk_func)
    return list(result)


class AsyncFileAttachment:
    """Async version of file attachment operations."""
    
    def __init__(self, attachment_model):
        self.attachment_model = attachment_model

    async def async_file_read(self, fname):
        """Async version of _file_read."""
        full_path = self.attachment_model._full_path(fname)
        return await async_file_read(full_path)

    async def async_file_write(self, bin_value, checksum):
        """Async version of _file_write."""
        fname, full_path = self.attachment_model._get_path(bin_value, checksum)
        
        if not await async_file_exists(full_path):
            success = await async_file_write(full_path, bin_value)
            if success:
                # add fname to checklist, in case the transaction aborts
                self.attachment_model._mark_for_gc(fname)
        
        return fname

    async def async_file_delete(self, fname):
        """Async version of _file_delete."""
        # simply add fname to checklist, it will be garbage-collected later
        self.attachment_model._mark_for_gc(fname)

    async def async_gc_file_store(self):
        """Async version of _gc_file_store."""
        # This is a simplified async version
        # The full implementation would need async database operations
        loop = asyncio.get_event_loop()
        gc_func = partial(self.attachment_model._gc_file_store)
        return await loop.run_in_executor(None, gc_func)


# Async context manager for file operations
class AsyncFileManager:
    """Context manager for async file operations."""
    
    def __init__(self, file_path, mode='rb'):
        self.file_path = file_path
        self.mode = mode
        self.file = None

    async def __aenter__(self):
        self.file = await aiofiles.open(self.file_path, self.mode)
        return self.file

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.file:
            await self.file.close()


# Async batch file operations
async def async_batch_file_read(file_paths, max_concurrent=10):
    """Read multiple files concurrently."""
    semaphore = asyncio.Semaphore(max_concurrent)
    
    async def read_with_semaphore(path):
        async with semaphore:
            return await async_file_read(path)
    
    tasks = [read_with_semaphore(path) for path in file_paths]
    return await asyncio.gather(*tasks, return_exceptions=True)


async def async_batch_file_write(file_data_pairs, max_concurrent=10):
    """Write multiple files concurrently."""
    semaphore = asyncio.Semaphore(max_concurrent)
    
    async def write_with_semaphore(path, data):
        async with semaphore:
            return await async_file_write(path, data)
    
    tasks = [write_with_semaphore(path, data) for path, data in file_data_pairs]
    return await asyncio.gather(*tasks, return_exceptions=True)


# Async file streaming for large files
async def async_file_stream_read(file_path, chunk_size=8192):
    """Stream read large files asynchronously."""
    try:
        async with aiofiles.open(file_path, 'rb') as f:
            while True:
                chunk = await f.read(chunk_size)
                if not chunk:
                    break
                yield chunk
    except (IOError, OSError) as e:
        _logger.error("Error streaming file %s: %s", file_path, e)
        raise


async def async_file_stream_write(file_path, data_stream, chunk_size=8192):
    """Stream write large files asynchronously."""
    try:
        dirname = os.path.dirname(file_path)
        if dirname and not os.path.exists(dirname):
            await async_makedirs(dirname)
        
        async with aiofiles.open(file_path, 'wb') as f:
            async for chunk in data_stream:
                await f.write(chunk)
        return True
    except (IOError, OSError) as e:
        _logger.error("Error streaming write to file %s: %s", file_path, e)
        return False


# Utility functions for async file operations
async def async_copy_file(src_path, dst_path):
    """Copy file asynchronously."""
    try:
        data = await async_file_read(src_path)
        return await async_file_write(dst_path, data)
    except Exception as e:
        _logger.error("Error copying file from %s to %s: %s", src_path, dst_path, e)
        return False


async def async_move_file(src_path, dst_path):
    """Move file asynchronously."""
    try:
        if await async_copy_file(src_path, dst_path):
            return await async_file_delete(src_path)
        return False
    except Exception as e:
        _logger.error("Error moving file from %s to %s: %s", src_path, dst_path, e)
        return False


# Monkey patch for ir.attachment to add async methods
def patch_ir_attachment():
    """Add async methods to ir.attachment model."""
    try:
        from odoo.addons.base.models.ir_attachment import IrAttachment
        
        async def async_file_read_patched(self, fname):
            return await async_file_read(self._full_path(fname))
        
        async def async_file_write_patched(self, bin_value, checksum):
            async_attachment = AsyncFileAttachment(self)
            return await async_attachment.async_file_write(bin_value, checksum)
        
        async def async_file_delete_patched(self, fname):
            async_attachment = AsyncFileAttachment(self)
            return await async_attachment.async_file_delete(fname)
        
        # Add async methods to the class
        IrAttachment.async_file_read = async_file_read_patched
        IrAttachment.async_file_write = async_file_write_patched
        IrAttachment.async_file_delete = async_file_delete_patched
        
        _logger.info("Successfully patched ir.attachment with async file methods")
        
    except ImportError:
        _logger.warning("Could not patch ir.attachment - module not available")
    except Exception as e:
        _logger.error("Error patching ir.attachment: %s", e)


# Apply patches when module is loaded
try:
    patch_ir_attachment()
except Exception as e:
    _logger.warning("Could not apply ir.attachment patches: %s", e)
